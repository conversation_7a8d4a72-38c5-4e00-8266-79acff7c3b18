import { acceptCookieConsent } from "../utils/acceptCookieConsent.js";
import { autoScrollToBottom } from "../utils/autoScrollToBottom.js";
import { checkSelectors } from "../utils/checkSelectors.js";
import { groupAdsByResponsive } from "../utils/groupAdsByResponsive.js";
import { runPlacementCheck } from "./runPlacementCheck.js";
import { runVisibilityCheck } from "./runVisibilityCheck.js";

export async function runChecksForDevice(browser, url, deviceName, config, activeLayers, consentState) {
    const page = await browser.newPage();
    page.on('console', msg => console.log(`[${config.name} BROWSER LOG]: ${msg.text()}`));

    try {
        console.log(`\n--- Test device: ${config.name} (${config.width}x${config.height}) ---`);
        await page.setViewport({ width: config.width, height: config.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        if (!consentState.handled) {
            console.log(`[${config.name}] First device check: Trying to accept cookie consent...`);
            const consentResult = await acceptCookieConsent(page);
            if (consentResult === 1) {
                console.log(`[${config.name}] Cookie consent accepted successfully.`);
                consentState.handled = true;
            } else {
                throw new Error(`[${config.name}] Cookie consent could not be accepted (Code: ${consentResult}).`);
            }
        }
        
        console.log(`[${config.name}] Wait for TCF API confirmation... ⏳ `);
        try {
            await page.evaluate(() => {
                return new Promise((resolve, reject) => {
                    // Retry mechanism to wait for the TCF API to be ready and successful
                    const checkTcfApi = (retries = 20) => { // ~10 seconds timeout (20 * 500ms)
                        if (typeof window.__tcfapi === 'function') {
                            window.__tcfapi('getTCData', 2, (tcData, success) => {
                                if (success) {
                                    console.log('TCF API success:', tcData);
                                    resolve();
                                } else if (retries > 0) {
                                    setTimeout(() => checkTcfApi(retries - 1), 500);
                                } else {
                                    reject(new Error('TCF API did not return success within the timeout.'));
                                }
                            });
                        } else if (retries > 0) {
                            // If the API function is not yet available, wait and retry
                            setTimeout(() => checkTcfApi(retries - 1), 500);
                        } else {
                            reject(new Error('__tcfapi function not found on page within the timeout. ❌ '));
                        }
                    };
                    checkTcfApi();
                });
            });
            console.log(`[${config.name}] TCF API confirmation received successfully. ✅ `);
        } catch (e) {
            console.warn(`[${config.name}] Error waiting for TCF API: ${e.message} ❌ `);
        }

        // A short wait can help stabilize the page before scrolling.
        await new Promise(resolve => setTimeout(resolve, 1000));

        // lazy load to ensure all content has been loaded before checking the ads.
        await autoScrollToBottom(page);

        // we extract the ads from the QMN object 
        const qmnConfig = await checkSelectors(page);
        if (!qmnConfig) {
            throw new Error(`[${config.name}] Could not retrieve window.qmn.config object.`);
        }

        // we group the ads by responsive type (desktop/mobile)
        const adsByResponsive = groupAdsByResponsive(qmnConfig);
        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`);

        // we run the checks for each layer (placement, visibility) and count the issues.
        const issues = {};
        let issueCount = 0;

        if (adsToCheck.length > 0) {
            if (activeLayers.includes('placement')) {
                issueCount += await runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }

            if (activeLayers.includes('visibility')) {
                issueCount += await runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
        } else {
            console.log(`[${deviceName}] No ads to check for device type '${responsiveKey}'.`);
        }

        return { deviceName, responsiveKey, issues, issueCount, error: null };

    } catch (e) {
        console.error(`❌ Error with device ${deviceName}:`, e.message);
        return { deviceName, error: e.message, issues: {}, issueCount: 1 };
    } finally {
        await page.close();
    }
}
