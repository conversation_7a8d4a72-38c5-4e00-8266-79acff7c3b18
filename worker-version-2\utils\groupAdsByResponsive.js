export const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN object or qmn.adSlots is not in the expected format.');
    return {};
  }

  const acc = qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    // Ignore ads of type 'tracker' and slots without ID/responsive.
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Make sure the array for the device type exists.
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Add display object to list.
    acc[responsive].push({ id, type });

    return acc;
  }, {});

  if (qmn.watchbetter) {
    acc.mobile.push({ id: 'watchbetter-embed', type: 'watchbetter' });
    acc.desktop.push({ id: 'watchbetter-embed', type: 'watchbetter' });   
  }

  return acc
};
