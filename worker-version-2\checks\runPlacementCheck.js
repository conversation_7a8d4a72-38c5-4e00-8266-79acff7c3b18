import { checkPlacementLogic } from '../browser-logic/checkPlacementLogic.js';

export async function runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unplacedAds = await page.evaluate(checkPlacementLogic, adsToCheck);
    if (unplacedAds.length > 0) {
        if (!issues.placement) issues.placement = { desktop: [], tablet: [],mobile: [] };
        issues.placement[responsiveKey] = unplacedAds;
        console.log(`[${deviceName}] ERROR [Placement]: ${unplacedAds.length} Display containers not found in the DOM:`, unplacedAds.map(ad => ad.id));
    } else {
        console.log(`[${deviceName}] SUCCESS [Placement]: All ${adsToCheck.length} display containers found.`);
    }
    return unplacedAds.length;
}
