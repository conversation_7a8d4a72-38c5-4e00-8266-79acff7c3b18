import { getElementId } from '../utils/getElementId.js';
import { checkVisibilityLogic } from '../browser-logic/checkVisibilityLogic.js';
import { isElementInViewportLogic } from '../browser-logic/isElementInViewportLogic.js';

export async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`### ➡️ [${deviceName}] SUCCESS [Visibility]: No ads to check for this layer.`);
        return 0; // there is nothing to check, so we return 0 issues.
    }

    const notVisibleAds = [];
    console.log(`### ➡️ [${deviceName}] Starting individual visibility check for ${adsToCheck.length} ads...`);

    for (const ad of adsToCheck) {
        let iframeHandle = null;

        // parentContainerHandle saves the parent container of the iframe. It is saved because it is part of the exclusion list.
        // the exclusion list is a list of elements that are not considered as possible occluder when checking if the ad is occluded 
        let parentContainer<PERSON>andle = null;
        let parentContainerId = null;
        let insContainerId = null;

        try {
            if (ad.type === 'watchbetter') {
                // whatchbetter has a different structure. 
                // --- Specific logic for watchbetter ---
                console.log(`### ➡️  [${deviceName}] ➡️ Checking ad ID: ${ad.id} (watchbetter). Searching for container: #${ad.id}`);
                const watchbetterContainerHandle = await page.$(`#${ad.id}`);
                if (watchbetterContainerHandle) {
                    parentContainerId = await getElementId(watchbetterContainerHandle);
                    iframeHandle = await watchbetterContainerHandle.$('iframe');
                    if (iframeHandle) {
                        parentContainerHandle = watchbetterContainerHandle;
                        console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside #${parentContainerId} for watchbetter ad.`);
                    }
                }
                
            } else {
                // --- Previous logic for QMN ads ---
                
                // we search for the div with the id "qmn_[digit]" e.g qmn64643

                const qmnDivSelector = `div[id^="qmn"][id*="${ad.id}"]`;
                console.log(`### ➡️  [${deviceName}] ➡️ Checking ad ID: ${ad.id}. Searching for div: ${qmnDivSelector}`);

                const qmnDivHandles = await page.$$(qmnDivSelector); // return an array
                

                if (qmnDivHandles.length === 0) {
                    console.log(`### ➡️  [${deviceName}] ⚠️ WARN: No div element matching "${qmnDivSelector}" found for ad.id ${ad.id}.`);
                    // Fallback to the 'asmobj_' logic if the qmn div is not found.
                    const asmobjContainerSelector = `div[id*="asmobj_${ad.id}"]`;
                    const asmobjContainerHandle = await page.$(asmobjContainerSelector);
                    if (asmobjContainerHandle) {
                        parentContainerId = await getElementId(asmobjContainerHandle);
                        iframeHandle = await asmobjContainerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 5000 });
                        if (iframeHandle) {
                            parentContainerHandle = asmobjContainerHandle;
                            console.log(`### ➡️ [${deviceName}] ✅ Found iframe via 'asmobj_' fallback for ad.id ${ad.id}. Parent: ${parentContainerId}`);
                        }
                    }

                    if (!iframeHandle) {
                        const details = { isTrulyVisible: false, reason: `No container div (${qmnDivSelector} or fallback) found for ad.id.` };
                        notVisibleAds.push({ ...ad, visibilityDetails: details });
                        console.log(`### ➡️ [${deviceName}]   -   Ad ID ${ad.id}: Container div not found.`);
                        continue;
                    }

                } else {
                    // Iterate through found QMN divs and search for all ins 
                    for (const qmnDiv of qmnDivHandles) {
                        parentContainerId = await getElementId(qmnDiv);
                        console.log(`### ➡️ [${deviceName}]   - Found QMN div: #${parentContainerId}. Searching for ins...`);

                        const insHandles = await qmnDiv.$$('ins');

                        if (insHandles.length === 0) {
                            console.log(`### ➡️ [${deviceName}]     - No 'ins' element found within div #${parentContainerId}.`);
                            const divOuterHTML = await qmnDiv.evaluate(node => node.outerHTML);
                            console.log(`### ➡️ [${deviceName}]     - Full HTML of div #${parentContainerId} (no ins found):`);
                            console.log(divOuterHTML);
                            continue;
                        }

                        for (const ins of insHandles) {
                            insContainerId = await getElementId(ins);
                            console.log(`### ➡️ [${deviceName}]     - Found ins: #${insContainerId}. Searching for iframe...`);
                            iframeHandle = await ins.$('iframe');

                            if (iframeHandle) {
                                parentContainerHandle = qmnDiv;
                                console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside div #${parentContainerId} -> ins #${insContainerId} for ad.id ${ad.id}.`);
                                break;
                            }
                        }

                        if (iframeHandle) {
                            break;
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`### ➡️ [${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`);
            const details = { isTrulyVisible: false, reason: `Error during iframe discovery: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            continue;
        }

        // --- Common logic after the iframe search ---
        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found after hierarchical search." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe not found after all attempts.`);
            if (parentContainerHandle) await parentContainerHandle.dispose();
            continue;
        }

        let adSelector;
        if (ad.type === 'watchbetter') {
            // For watchbetter, the iframe has no ID. We build a selector based on its parent.
            // The parentContainerId was already found in the try-block.
            adSelector = `#${parentContainerId} > iframe`;
            console.log(`### ➡️ [${deviceName}] -  Using selector for watchbetter ad: ${adSelector}`);
        } else {
            // For all other ads, we expect an ID on the iframe.
            const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
            console.log(`### ➡️ [${deviceName}] -  Iframe ID found for Ad ID ${ad.id}: ${iframeId}`);

            if (!iframeId) {
                const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe found, but it has no ID.`);
                if (parentContainerHandle) await parentContainerHandle.dispose();
                await iframeHandle.dispose();
                continue;
            }
            adSelector = `#${iframeId}`;
        }

        // --- Ad-Type-Specific Visibility Logic ---
        console.log(`### ➡️ [${deviceName}]   - Applying visibility logic for ad type: ${ad.type}`);

        let floorAdButtonHandle = null; // Handle for the floorad button

        switch (ad.type) {
            case 'floorad':
                console.log(`### ➡️ [${deviceName}]  Specific logic for 'floorad' is applied.`);
                const floorAdButtonSelector = 'div[id^="mflbuttons-"]';
                floorAdButtonHandle = await page.$(floorAdButtonSelector);

                if (floorAdButtonHandle) {
                    console.log(`### ➡️ [${deviceName}]  Floorad button found. Click to open the ad.`);
                    await floorAdButtonHandle.click();
                    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for animation
                } else {
                    console.log(`### ➡️ [${deviceName}]  WARN: Floorad button (${floorAdButtonSelector}) not found.`);
                }
                break;

            case 'sitebar':
                console.log(`### ➡️ [${deviceName}]  Specific logic for 'sitebar' is applied.`);
                if (!parentContainerHandle) {
                    console.log(`### ➡️ [${deviceName}]  WARN: Parent container handle for sitebar ad ${ad.id} not found. Scroll logic skipped.`);
                    break;
                }

                let isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                   
                    return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });

                });
                
                console.log(`### ➡️ [${deviceName}]  Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`);

                if (isContainerVisibleByCss) {
                    console.log(`### ➡️ [${deviceName}]  Sitebar container for ad ${ad.id} is already visible via CSS.`);
                } else {
                    console.log(`### ➡️ [${deviceName}]  Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`);
                    
                    const maxScrollAttempts = 50; // Maximum number of scroll attempts
                    let attempts = 0;

                    const scrollPosition = await page.evaluate(() => ({
                        scrollTop: window.scrollY,
                        scrollHeight: document.body.scrollHeight,
                        clientHeight: document.documentElement.clientHeight
                    }));
                    
                    const isCloseToBottom = (scrollPosition.scrollTop + scrollPosition.clientHeight) >= (scrollPosition.scrollHeight - 100);
                    const scrollDirection = isCloseToBottom ? 1 : -1; // -1 for up, 1 for down
                    const scrollAmount = 120; // Pixels per attempt

                    console.log(`### ${isCloseToBottom}`)

                    console.log(`### ➡️ [${deviceName}]   - Scroll ${scrollDirection === 1 ? 'DOWN' : 'UP'}, to find the sitebar container.`);

                    while (!isContainerVisibleByCss && attempts < maxScrollAttempts) {
                        await page.evaluate((y) => { window.scrollBy(0, y); }, scrollDirection * scrollAmount);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                            if (typeof el.checkVisibility !== 'function') return false;
                            return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                        });
                        attempts++;

                        const newScrollTop = await page.evaluate(() => window.scrollY);
                        if ((scrollDirection === -1 && newScrollTop === 0) || 
                            (scrollDirection === 1 && (await page.evaluate(() => (window.innerHeight + window.scrollY) >= document.body.scrollHeight)))) {
                            console.log(`### ➡️ [${deviceName}]   - page ${scrollDirection === 1 ? 'end' : 'start'} Reached. Stop the scroll search.`);
                            break;
                        }
                    }

                    if (isContainerVisibleByCss) {
                        console.log(`### ➡️ [${deviceName}]   - Sitebar container is now visible after ${attempts} scroll attempts via CSS.`);
                    } else {
                        console.log(`### ➡️ [${deviceName}]   - WARN: Could not make the sitebar container visible after ${attempts} attempts via CSS.`);
                    }
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;

            case 'intext':
            case 'prospekt':
            case 'hpa':
            default: // Standard logic for known and unknown types
                console.log(`### ➡️ [${deviceName}]   - Standard logic (scrollIntoView) is applied.`);
                // Scroll the element into the center of the viewport, to ensure it is checkable.
                await iframeHandle.evaluate(el => {
                    const rect = el.getBoundingClientRect();
                    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                    // Calculate the target scroll position to place the element 200px above the center
                    const targetY = rect.top + window.scrollY - (viewportHeight / 2) + (rect.height / 2) ;
                    window.scrollTo({ top: targetY });
                });
                // A short wait can help stabilize the page before scrolling.
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;
        }

        // DEBUG: Check if the element is in the viewport after the action.
        const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
        console.log(`### ➡️ [${deviceName}]   - DEBUG: Element ${adSelector} in viewport after action: ${isElementInViewportForDebug}`);
       

        // Evaluate the visibility of this single, now centered element.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0];

        if (!result || result.visibilityPercentage < 100) {
            let reason = 'Others'; // Default reason
            
            if (result.reason === 'Element not found') {
                reason = 'Element not found';
            } else if (!result.isVisibleByCss) {
                reason = 'Hidden by CSS';
            } else if (result.isOccluded) {
                reason = 'Covered';
            } else if (result.isPartiallyOutOfViewport) {
                reason = 'Not in viewport';
            } else if (!result.isWithinPageBounds) {
                reason = 'Not within page bounds';
            }

            const details = {
                visibilityPercentage: result.visibilityPercentage,
                reason: reason,
                isVisibleByCss: result.isVisibleByCss,
                occludingElements: result.occludingElements
            };

            notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]   - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`);
        } else {
            console.log(`### ➡️ [${deviceName}]   - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`);
        }

        // Close the Floorad ad if it was opened
        if (floorAdButtonHandle) {
            console.log(`### ➡️ [${deviceName}]  Close Floorad ad by clicking again.`);
            try {
                await floorAdButtonHandle.click();
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for animation
            } catch (e) {
                console.log(`### ➡️ [${deviceName}]  WARN: Floorad button could not be clicked again (possibly already closed): ${e.message}`);
            } finally {
                await floorAdButtonHandle.dispose();
            }
        }

        if (parentContainerHandle) {
            await parentContainerHandle.dispose();
        }
        await iframeHandle.dispose(); // Clean up the iframe handle
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`);
    } else {
        console.log(`[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`);
    }
    return notVisibleAds.length;
}
