# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock


# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
out/
.next/

# Logs
logs/
*.log

# Docker
.docker/
docker-compose.override.yml

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.windsurfrules
CLAUDE.md 
.cursor/
.superdesign/

# Testing
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
.aider*
